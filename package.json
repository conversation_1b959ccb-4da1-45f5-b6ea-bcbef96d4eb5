{"name": "shadcnblocks-admin", "version": "1.0.0", "author": "Shadcnblocks.com", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "format": "prettier --check .", "format:fix": "prettier --write .", "lint": "next lint", "lint:fix": "next lint --fix"}, "dependencies": {"@faker-js/faker": "^9.3.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/react-visually-hidden": "^1.1.2", "@tabler/icons-react": "^3.22.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-table": "^8.20.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.4", "country-region-data": "^3.1.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "lucide-react": "^0.475.0", "next": "15.1.7", "next-themes": "^0.4.4", "react": "19.0.0", "react-day-picker": "8.10.1", "react-dom": "19.0.0", "react-hook-form": "^7.53.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.23.8"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@faker-js/faker": "^9.2.0", "@tailwindcss/postcss": "^4.0.7", "@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/node": "^20.17.14", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.7", "typescript": "^5.7.3"}}