import { IconDots } from "@tabler/icons-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export default function LogsAction() {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="shrink-0" variant="outline" size="icon">
          <IconDots className="rotate-90" size={20} strokeWidth={1.5} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[150px]">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>Export to CSV</DropdownMenuItem>
        <DropdownMenuItem>Export to JSON</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
