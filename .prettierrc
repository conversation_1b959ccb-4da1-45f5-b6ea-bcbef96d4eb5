{"arrowParens": "always", "semi": false, "tabWidth": 2, "printWidth": 80, "singleQuote": false, "trailingComma": "es5", "bracketSpacing": true, "endOfLine": "lf", "plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "importOrder": ["^react$", "^react-dom/client$", "^react/(.*)$", "^globals$", "^zod$", "^axios$", "^date-fns$", "^react-hook-form$", "^@radix-ui/(.*)$", "^@tabler/icons-react$", "<THIRD_PARTY_MODULES>", "^@/assets/(.*)", "^@/lib/(.*)$", "^@/utils/(.*)$", "^@/constants/(.*)$", "^@/context/(.*)$", "^@/hooks/(.*)$", "^@/components/layouts/(.*)$", "^@/components/ui/(.*)$", "^@/components/errors/(.*)$", "^@/components/(.*)$", "^[./]"]}